// Copyright Isto Inc.

using SnowEater.Core.Scripts.Localization;
using SnowEater.Core.Systems;
using System;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Settings;
using System.Linq;

namespace SnowEater.Core.Services
{
    public class UnityLocalization : Service, ILocalization
    {
        public override Type GetServiceInterface() => typeof(ILocalization);

        public async void SetLanguage(LocalizationLanguageEnum language)
        {
            // Ensure the localization system is initialized
            await LocalizationSettings.InitializationOperation.Task;

            // Find the locale matching the enum's locale code
            var locale = LocalizationSettings.AvailableLocales.Locales
                .FirstOrDefault(l => l.Identifier.Code == language.Value);

            if (locale != null)
            {
                LocalizationSettings.SelectedLocale = locale;
            }
            else
            {
                Debug.LogWarning($"Locale '{language.Value}' not found. Available: {string.Join(\", \", LocalizationSettings.AvailableLocales.Locales.Select(l => l.Identifier.Code))}");
            }
        }

        public string GetLocalizedString(string key)
        {
            throw new NotImplementedException();
        }
    }
}