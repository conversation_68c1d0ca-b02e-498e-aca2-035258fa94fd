// 2025-05-28 AI-Tag
// This was created with the help of <PERSON>, a Unity Artificial Intelligence product.

// Copyright Isto Inc.

using SnowEater.Core.Managers.WindowManager;
using SnowEater.Core.Services;
using SnowEater.Core.UI.WindowData;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.UI.Modals
{
    [UxmlElement]
    public partial class YesNoModal : Window<YesNoModalData>
    {
        private static readonly string TITLE_LABEL = "yes-no-modal__title";
        private static readonly string CONTENT_LABEL = "yes-no-modal__content";
        private static readonly string CONFIRM_BUTTON = "yes-no-modal__confirm-btn";
        private static readonly string CANCEL_BUTTON = "yes-no-modal__cancel-btn";

        private Label _titleLabel;
        private Label _contentLabel;
        private Button _confirmButton;
        private Button _cancelButton;



        public YesNoModal() { }

        ~YesNoModal() { }

        public override void ShowWindow()
        {
            base.ShowWindow();
            Initialize();
        }

        public override void SetWindowData(YesNoModalData windowData)
        {
            base.SetWindowData(windowData);
            Configure(WindowData.Title, WindowData.Content, WindowData.ShowConfirmButton, WindowData.ConfirmButtonText, WindowData.ShowCancelButton, WindowData.CancelButtonText);
        }

        private void Initialize()
        {
            // Query UI elements
            _titleLabel = contentContainer.Q<Label>(TITLE_LABEL);
            _contentLabel = contentContainer.Q<Label>(CONTENT_LABEL);
            _confirmButton = contentContainer.Q<Button>(CONFIRM_BUTTON);
            _cancelButton = contentContainer.Q<Button>(CANCEL_BUTTON);

            // Ensure buttons are focusable
            if (_confirmButton != null)
            {
                _confirmButton.focusable = true;
                _confirmButton.RegisterCallback<NavigationSubmitEvent>(OnConfirm_Submitted);
                _confirmButton.RegisterCallback<ClickEvent>(OnConfirm_Submitted);
            }

            if (_cancelButton != null)
            {
                _cancelButton.focusable = true;
                _cancelButton.RegisterCallback<NavigationSubmitEvent>(OnCancel_Submitted);
                _cancelButton.RegisterCallback<ClickEvent>(OnCancel_Submitted);
            }
        }

        /// <summary>
        /// Configures the modal dynamically.
        /// </summary>
        /// <param name="title">The title text of the modal.</param>
        /// <param name="content">The content text of the modal.</param>
        /// <param name="showConfirmButton">Whether to show the confirm button.</param>
        /// <param name="confirmButtonText">The text for the confirm button.</param>
        /// <param name="showCancelButton">Whether to show the cancel button.</param>
        /// <param name="cancelButtonText">The text for the cancel button.</param>
        public void Configure(string title, string content, bool showConfirmButton, string confirmButtonText, bool showCancelButton, string cancelButtonText)
        {
            // Set title and content
            if (_titleLabel != null)
                _titleLabel.text = title;

            if (_contentLabel != null)
                _contentLabel.text = content;

            // Configure confirm button
            if (_confirmButton != null)
            {
                _confirmButton.style.display = showConfirmButton ? DisplayStyle.Flex : DisplayStyle.None;
                _confirmButton.text = confirmButtonText;
            }

            // Configure cancel button
            if (_cancelButton != null)
            {
                _cancelButton.style.display = showCancelButton ? DisplayStyle.Flex : DisplayStyle.None;
                _cancelButton.text = cancelButtonText;
            }
        }

        private void OnConfirm_Submitted(EventBase evt)
        {
            // Handle confirm button click
            Debug.Log("Confirm button clicked.");
            // CloseWindow();
        }

        private void OnCancel_Submitted(EventBase evt)
        {
            // Handle cancel button click
            Debug.Log("Cancel button clicked.");
            // CloseWindow();
        }
    }
}