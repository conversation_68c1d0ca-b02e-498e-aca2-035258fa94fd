// Copyright Snow Eater Studios

using SnowEater.Core.Managers.WindowManager;
using SnowEater.Core.Services;
using SnowEater.Core.UI.Modals;
using SnowEater.Core.UI.WindowData;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.UI
{
    [UxmlElement]
    public partial class TitleScreen : Window
    {
        private static readonly string START_NEW_GAME_BUTTON = "title-screen__start-new-game-btn";

        VisualElement _startNewGameButton;
        protected IWindowManagerService _windowManagerService;


        public TitleScreen()
        {
            // HomeScreenController.ShowLevelInfo += OnShowLevelInfo;
        }

        ~TitleScreen()
        {
            // HomeScreenController.ShowLevelInfo -= OnShowLevelInfo;
        }

        public override void ShowWindow()
        {
            base.ShowWindow();
            Initialize();
            _windowManagerService = Systems.Services.Get<IWindowManagerService>();
        }

        void Initialize()
        {
            _startNewGameButton = contentContainer.Q(START_NEW_GAME_BUTTON);
            if (_startNewGameButton != null)
            {
                // Ensure the element can be focused by navigation.
                _startNewGameButton.focusable = true;
                Focus(); // Focus to this window panel first. //TODO: Perhaps this needs to get integrated into base.ShowWindow()? Might cause issue's if there are multiple windows though.
                _startNewGameButton.Focus();
                // Register callbacks for both mouse and controller/keyboard input.
                _startNewGameButton.RegisterCallback<ClickEvent>(OnSubmit_ReturnToGame);
                _startNewGameButton.RegisterCallback<NavigationSubmitEvent>(OnSubmit_ReturnToGame);
            }
        }

        private void OnSubmit_ReturnToGame(EventBase evt)
        {
            var data = new YesNoModalData
            {
                Title             = "Are you sure?",
                Content           = "Do you want to proceed with this action?",
                ShowConfirmButton = true,
                ConfirmButtonText = "Yes",
                ShowCancelButton  = true,
                CancelButtonText  = "No",
                OnCancel = () =>
                {
                    Debug.Log("Cancelled");
                },
                OnConfirm = () =>
                {
                    Debug.Log("Confirmed");
                }
                // you can also set data.overrideLayer = "ModalLayer";
            };

            _windowManagerService.PushWindow<YesNoModal, YesNoModalData>(data);
        }

    }
}